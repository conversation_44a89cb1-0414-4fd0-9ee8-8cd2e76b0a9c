function varargout = createLogFile()
    % @brief Create a log file with a timestamp in the filename.
    % @return logfile: char array with filename

    
    try
        % Get current date and time
        t = datetime('now','Format','yyyyMMdd_HHmm');
        % Create filename like log_20250913_0857.txt
        logfile = sprintf('log_%s.txt', char(t));
        
        % Create the file and write header
        fid = fopen(logfile, 'a+');
        if fid == -1
            error('Could not create log file: %s', logfile);
        end
        fprintf(fid, 'Log created: %s\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
        fclose(fid);
    catch exception
        error('createLogFile: An error occurred: %s.\n', exception.message);
        
    end
    
    % Return the filename
    if nargout > 0
        varargout{1} = logfile;
    end
    
end