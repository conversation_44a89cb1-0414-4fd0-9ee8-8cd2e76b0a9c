function writeLog(logfile, msg, varargin)
    % logfile: char array with filename (output of createLogFile)
    % msg:     message string (printf-style)
    % varargin: optional format arguments
    
    fid = fopen(logfile, 'a');
    if fid == -1
        error('Could not open log file: %s', logfile);
    end
    % Add timestamp for each line
    timestamp = datestr(now, 'yyyy-mm-dd HH:MM:SS');
    fprintf(fid, '[%s] %s\n', timestamp, sprintf(msg, varargin{:}));
    fclose(fid);
end