function out = DEBUG(arg1, varargin)
% DEBUG - prints debug information to screen (legacy style) and also appends
%         the same info to a temporary 'log.txt'.
%
% Usage:
%   DEBUG(obj)                     % old behavior: prints to screen and logs
%   DEBUG('finalize')              % copy log.txt -> log_YYYYMMDD_HHMM.txt (keeps temp)
%   DEBUG('finalize', false)       % copy and delete temp log.txt
%
% Returns (only for finalize):
%   out = DEBUG('finalize')        % out is char array of final filename

    % Respect existing debug mode global
    global DEBUG_MODE;
    if ~exist('DEBUG_MODE', 'var') || ~DEBUG_MODE
        return;
    end

    % persistent state (singleton-ish)
    persistent tempFileInitialized tempFileName
    if isempty(tempFileInitialized) || isempty(tempFileName)
        tempFileName = 'log.txt';
        % create/overwrite temp file and write header
        fid0 = fopen(tempFileName, 'w');
        if fid0 == -1
            warning('DEBUG:InitFailed', 'Could not create temp log file: %s', tempFileName);
        else
            fprintf(fid0, 'Temporary log started: %s\n\n', datestr(now,'yyyy-mm-dd HH:MM:SS'));
            fclose(fid0);
        end
        tempFileInitialized = true;
    end

    % If called with a finalize command
    if ischar(arg1) || isstring(arg1)
        cmd = char(arg1);
        if strcmpi(cmd, 'finalize')
            % handle optional keepTemp flag
            keepTemp = true;
            if ~isempty(varargin)
                keepTemp = logical(varargin{1});
            end
            if ~isfile(tempFileName)
                error('DEBUG:NoTempFile', 'Temporary log file not found: %s', tempFileName);
            end

            % build timestamped target filename
            t = datetime('now','Format','yyyyMMdd_HHmm');
            base = sprintf('log_%s.txt', char(t));
            finalName = findUniqueFilename(base);

            ok = copyfile(tempFileName, finalName);
            if ~ok
                error('DEBUG:FinalizeFailed', 'Could not copy %s -> %s', tempFileName, finalName);
            end
            if ~keepTemp
                try
                    delete(tempFileName);
                catch
                    warning('DEBUG:DeleteTempFailed', 'Could not delete temp file: %s', tempFileName);
                end
            end
            fprintf('Log finalized: %s\n', finalName);
            if nargout > 0
                out = char(finalName);
            end
            return;
        else
            error('DEBUG:UnknownCommand', 'Unknown DEBUG command: %s', cmd);
        end
    end

    % --- Normal debug printing (legacy behavior) ---
    obj = arg1;

    % Retrieve caller information
    % Keep similar to your original: try to get caller file, function and line
    st = dbstack('-completenames', 1);
    if numel(st) >= 1
        caller = st(1);
        % caller.file sometimes contains full path; keep as-is like legacy
        if isfield(caller, 'file')
            fname = caller.file;
        elseif isfield(caller, 'name')
            fname = caller.name;
        else
            fname = '<unknown>';
        end
        if isfield(caller, 'name')
            funcName = caller.name;
        else
            funcName = '<command-line>';
        end
        if isfield(caller, 'line')
            lineNum = caller.line;
        else
            lineNum = NaN;
        end
    else
        fname = '<unknown>';
        funcName = '<command-line>';
        lineNum = NaN;
    end

    % Print debug header to screen (preserve original output form)
    fprintf('[DEBUG] Called from file: %s\n', fname);
    fprintf('[DEBUG] In function: %s, at line: %d\n', funcName, lineNum);

    % Display object/value and its class (preserve behavior)
    try
        disp(obj);
    catch
        fprintf('Could not display object directly.\n');
    end
    fprintf('Type/Class: %s\n\n', class(obj));

    % --- Also append the same info into temp log file ---
    try
        fid = fopen(tempFileName, 'a');
        if fid ~= -1
            ts = datestr(now,'yyyy-mm-dd HH:MM:SS');
            fprintf(fid, '[%s] [DEBUG] Called from file: %s\n', ts, fname);
            fprintf(fid, '[%s] [DEBUG] In function: %s, at line: %d\n', ts, funcName, lineNum);

            % capture disp output to string (multi-line safe)
            try
                dispStr = evalc('disp(obj)');
                % write disp output prefixed with timestamp on each line
                dispLines = splitlines(dispStr);
                for k = 1:numel(dispLines)
                    if ~isempty(dispLines{k})
                        fprintf(fid, '[%s] %s\n', ts, dispLines{k});
                    end
                end
            catch
                fprintf(fid, '[%s] Could not display object directly.\n', ts);
            end

            fprintf(fid, '[%s] Type/Class: %s\n\n', ts, class(obj));
            fclose(fid);
        else
            warning('DEBUG:WriteFailed', 'Could not open temp log file for append: %s', tempFileName);
        end
    catch ME
        warning('DEBUG:LogWriteError', 'Error while writing to log: %s', ME.message);
    end
end

%% Helper: ensure unique filename by appending _1, _2, ...
function fname = findUniqueFilename(base)
    if ~isfile(base)
        fname = base;
        return;
    end
    [p, n, e] = fileparts(base);
    idx = 1;
    while true
        candidate = fullfile(p, sprintf('%s_%d%s', n, idx, e));
        if ~isfile(candidate)
            fname = candidate;
            return;
        end
        idx = idx + 1;
    end
end